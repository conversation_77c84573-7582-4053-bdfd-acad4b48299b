#!/usr/bin/env python3
"""
Deploy Gemini Search Engine as MCPO Container
"""

import os
import subprocess
import sys
import json
from pathlib import Path

# Configuration
CONTAINER_NAME = "gemini-search-mcpo-8001"
IMAGE_NAME = "gemini-search-mcpo"
HOST_PORT = 8001
CONTAINER_PORT = 8001

def create_dockerfile():
    """Create Dockerfile for Gemini Search Engine"""
    dockerfile_content = '''FROM ghcr.io/open-webui/mcpo:main

# Install additional dependencies
RUN pip install --no-cache-dir \\
    redis>=5.0.0 \\
    google-generativeai>=0.8.0 \\
    httpx>=0.24.0

# Copy gemini search engine server
COPY . /app/servers/gemini_search_engine/

# Create config for gemini search only
RUN echo '{ \\
  "mcpServers": { \\
    "gemini_search_engine": { \\
      "command": "python3.12", \\
      "args": ["/app/servers/gemini_search_engine/server.py"], \\
      "env": { \\
        "GEMINI_API_KEY": "AIzaSyDHsg-8CCSEBUO9N3V0vaRsCSNq2iS4oec", \\
        "GEMINI_MODEL_NAME": "gemini-2.5-flash-lite", \\
        "REDIS_URL": "redis://redis-persistent-cache:6379", \\
        "REDIS_DB": "0", \\
        "CACHE_TTL": "7200" \\
      } \\
    } \\
  } \\
}' > /app/config.json

EXPOSE 8001
'''
    
    with open('Dockerfile', 'w') as f:
        f.write(dockerfile_content)
    print("✅ Created Dockerfile")

def build_image():
    """Build Docker image"""
    print(f"🔨 Building Docker image: {IMAGE_NAME}")
    
    result = subprocess.run([
        'docker', 'build', 
        '-t', IMAGE_NAME,
        '.'
    ], capture_output=True, text=True)
    
    if result.returncode != 0:
        print(f"❌ Failed to build image: {result.stderr}")
        return False
    
    print("✅ Docker image built successfully")
    return True

def stop_existing_container():
    """Stop and remove existing container"""
    print(f"🛑 Stopping existing container: {CONTAINER_NAME}")
    
    # Stop container
    subprocess.run(['docker', 'stop', CONTAINER_NAME], 
                  capture_output=True, text=True)
    
    # Remove container
    subprocess.run(['docker', 'rm', CONTAINER_NAME], 
                  capture_output=True, text=True)
    
    print("✅ Existing container stopped and removed")

def run_container():
    """Run the container"""
    print(f"🚀 Starting container: {CONTAINER_NAME}")
    
    # Check if redis container exists
    redis_link = []
    result = subprocess.run(['docker', 'ps', '--filter', 'name=redis-persistent-cache', '--format', '{{.Names}}'], 
                          capture_output=True, text=True)
    if 'redis-persistent-cache' in result.stdout:
        redis_link = ['--link', 'redis-persistent-cache:redis-persistent-cache']
        print("🔗 Linking to Redis container")
    
    cmd = [
        'docker', 'run', '-d',
        '--name', CONTAINER_NAME,
        '-p', f'{HOST_PORT}:{CONTAINER_PORT}',
        '--restart', 'unless-stopped'
    ] + redis_link + [
        IMAGE_NAME,
        'mcpo', '--config', '/app/config.json', '--port', str(CONTAINER_PORT)
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode != 0:
        print(f"❌ Failed to start container: {result.stderr}")
        return False
    
    print(f"✅ Container started successfully")
    print(f"🌐 Gemini Search Engine available at: http://localhost:{HOST_PORT}")
    return True

def verify_deployment():
    """Verify the deployment"""
    import time
    import requests
    
    print("⏳ Waiting for container to start...")
    time.sleep(10)
    
    try:
        response = requests.get(f"http://localhost:{HOST_PORT}/docs", timeout=10)
        if response.status_code == 200:
            print("✅ Gemini Search Engine is running!")
            print(f"📖 API Documentation: http://localhost:{HOST_PORT}/docs")
            return True
        else:
            print(f"⚠️ Container started but API not ready (status: {response.status_code})")
            return False
    except Exception as e:
        print(f"❌ Failed to verify deployment: {e}")
        return False

def main():
    """Main deployment function"""
    print("🚀 Deploying Gemini Search Engine MCPO Container")
    print("=" * 50)
    
    # Change to gemini_search_engine directory
    os.chdir(Path(__file__).parent)
    
    try:
        # Step 1: Create Dockerfile
        create_dockerfile()
        
        # Step 2: Stop existing container
        stop_existing_container()
        
        # Step 3: Build image
        if not build_image():
            return False
        
        # Step 4: Run container
        if not run_container():
            return False
        
        # Step 5: Verify deployment
        if verify_deployment():
            print("\n🎉 DEPLOYMENT SUCCESSFUL!")
            print(f"🔍 Gemini Search Engine: http://localhost:{HOST_PORT}")
            print(f"📖 API Docs: http://localhost:{HOST_PORT}/docs")
            print(f"🛠️ Tools available:")
            print("   - redis_search: High-performance search with Redis caching")
            print("   - redis_stats: Cache performance statistics") 
            print("   - redis_cleanup: Cache maintenance")
            return True
        else:
            print("\n⚠️ DEPLOYMENT COMPLETED BUT VERIFICATION FAILED")
            return False
            
    except Exception as e:
        print(f"\n❌ DEPLOYMENT FAILED: {e}")
        return False
    finally:
        # Cleanup
        if os.path.exists('Dockerfile'):
            os.remove('Dockerfile')

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
