# Gemini Search Engine MCP Container

Standalone MCP server container for Gemini AI search with Redis caching.

## Features

- 🔍 **Gemini AI Search**: Powered by Google's Gemini 1.5 Pro
- 🚀 **Redis Caching**: Fast response times with persistent caching
- 📊 **Performance Stats**: Built-in performance monitoring
- 🔧 **Cache Management**: Advanced cache control and cleanup
- 🌐 **MCP Compatible**: Standard MCP server interface

## Quick Start

### Build and Run

```bash
# Build the container
docker build -t gemini-search-engine .

# Run with docker-compose
docker-compose up -d

# Or run standalone
docker run -d \
  --name gemini-search-engine \
  --network acca-network \
  -e GEMINI_API_KEY=your_api_key \
  -e REDIS_URL=redis://redis-persistent-cache:6379 \
  gemini-search-engine
```

### Environment Variables

- `GEMINI_API_KEY`: Google Gemini API key (required)
- `REDIS_URL`: Redis connection URL (default: redis://redis-persistent-cache:6379)
- `REDIS_DB`: Redis database number (default: 0)
- `CACHE_TTL`: Cache TTL in seconds (default: 7200)

## Available Tools

1. **gemini_search**: AI-powered search with caching
2. **gemini_stats**: Performance statistics
3. **gemini_cleanup**: Cache management

## Integration with MCPO

Add to MCPO config.json:

```json
{
  "gemini_search_engine": {
    "command": "docker",
    "args": ["exec", "-i", "gemini-search-engine", "python", "server.py"],
    "env": {}
  }
}
```
