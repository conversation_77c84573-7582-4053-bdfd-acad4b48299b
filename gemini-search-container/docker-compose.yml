version: '3.8'

services:
  gemini-search-engine:
    build: .
    container_name: gemini-search-engine
    networks:
      - acca-network
    environment:
      - GEMINI_API_KEY=AIzaSyDHsg-8CCSEBUO9N3V0vaRsCSNq2iS4oec
      - REDIS_URL=redis://redis-persistent-cache:6379
      - REDIS_DB=0
      - CACHE_TTL=7200
    depends_on:
      - redis-persistent-cache
    restart: unless-stopped
    stdin_open: true
    tty: true
    
  # Redis cache service (if not already running)
  redis-persistent-cache:
    image: redis:7-alpine
    container_name: redis-persistent-cache
    networks:
      - acca-network
    ports:
      - "6380:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes

networks:
  acca-network:
    external: true

volumes:
  redis_data:
